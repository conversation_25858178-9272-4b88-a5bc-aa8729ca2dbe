# hx - frame 后台管理系统需求说明书

## 一、引言

本文档旨在详细阐述 hx - frame 后台管理系统的需求，包括功能需求、非功能需求、系统架构设计、项目计划和风险评估等方面的内容，为项目的开发、测试、部署和维护提供明确的指导。

## 二、系统概述

hx - frame 后台管理系统是一款基于 Spring Boot
的开源后台管理系统，具有功能完善、易用性强等特点。该系统主要面向企业管理员和运营人员，帮助他们高效地管理企业资源、用户信息、业务数据等，提升企业的运营效率和管理水平。

## 三、功能需求

### （一）用户管理

1. **用户信息管理** ：支持添加、编辑、删除用户信息，包括用户名、密码、邮箱、手机号、角色等字段。
2. **用户状态管理** ：可对用户进行启用、禁用、锁定等操作，确保系统的安全性和稳定性。
3. **用户查询与检索** ：提供多种查询条件，如用户名、角色、部门等，快速定位所需用户。
4. **用户导入与导出** ：支持从 Excel 导入用户数据，也可将用户数据导出为 Excel 格式，方便数据迁移和备份。
5. **原型图**:

- 用户列表: ![](https://yc-imgs.oss-cn-shanghai.aliyuncs.com/20250509222714981.png)，其中左侧是部门树，
  右侧是用户列表。默认显示根部门的用户。点击具体部门，显示该部门的用户。
- 新增用户: ![](https://yc-imgs.oss-cn-shanghai.aliyuncs.com/20250509222945961.png)
- 更多功能：
   - 重置密码: ![](https://yc-imgs.oss-cn-shanghai.aliyuncs.com/20250509223116614.png)

```mermaid
graph TD
A[用户管理页面] --> B[添加用户表单]
A --> C[编辑用户表单]
A --> D[删除用户确认]
A --> E[用户搜索栏]
A --> F[用户列表]
A --> G[导入用户按钮]
A --> H[导出用户按钮]
```

### （二）角色管理

1. **角色创建与编辑** ：能够创建和修改角色信息，如角色名称、角色描述等。
2. **角色授权** ：为不同角色分配相应的菜单、按钮和数据权限，实现基于角色的访问控制。
3. **角色查询** ：通过角色名称、角色状态等条件快速查询角色信息。
4. **原型图**：

- 角色列表: ![](http://yc-imgs.oss-cn-shanghai.aliyuncs.com/img/20250509165254.png)
- 新增角色: ![](http://yc-imgs.oss-cn-shanghai.aliyuncs.com/img/20250509165339.png)

```mermaid
graph TD
A[角色管理页面] --> B[添加角色表单]
A --> C[编辑角色表单]
A --> D[删除角色确认]
A --> E[角色搜索栏]
A --> F[角色列表]
```

### （三）菜单管理

1. **菜单构建** ：支持创建多级菜单结构，包括菜单名称、路径、图标、顺序等属性设置。
2. **菜单授权** ：将菜单与不同角色关联，控制用户可访问的菜单范围。
3. **菜单查询与维护** ：可查询菜单信息，并对菜单进行编辑、删除等操作。
4. **原型图**:

- 菜单树: ![](http://yc-imgs.oss-cn-shanghai.aliyuncs.com/img/20250509162008.png)
- 新增菜单: ![](http://yc-imgs.oss-cn-shanghai.aliyuncs.com/img/20250509162106.png)

```mermaid
graph TD
A[菜单管理页面] --> B[添加菜单表单]
A --> C[编辑菜单表单]
A --> D[删除菜单确认]
A --> E[菜单搜索栏]
A --> F[菜单树形列表]
```

### （四）部门管理

1. **部门信息维护** ：添加、编辑、删除部门信息，涵盖部门名称、负责人、联系方式、上级部门等字段。
2. **部门结构展示** ：以树形结构直观呈现部门层级关系。
3. **部门查询** ：根据部门名称、部门负责人等条件查询部门信息。
4. **原型图** :

- 部门列表: ![](https://yc-imgs.oss-cn-shanghai.aliyuncs.com/20250507210054572.png)
- 新增部门: ![](https://yc-imgs.oss-cn-shanghai.aliyuncs.com/20250507210215530.png)

```mermaid
graph TD
A[部门管理页面] --> B[添加部门表单]
A --> C[编辑部门表单]
A --> D[删除部门确认]
A --> E[部门搜索栏]
A --> F[部门树形列表]
```

### （五）岗位管理

1. **岗位信息维护** ：支持添加、编辑、删除岗位信息，包括岗位名称、岗位编码、岗位排序、岗位状态等字段。
2. **岗位状态管理** ：可对岗位进行启用、禁用等操作，控制岗位的可用性。
3. **岗位查询与检索** ：提供按岗位名称、岗位编码、岗位状态等条件进行查询和筛选的功能。
4. **岗位排序** ：支持设置岗位的排序值，控制岗位在列表中的显示顺序。
5. **原型图**:

- 岗位列表: ![](http://yc-imgs.oss-cn-shanghai.aliyuncs.com/img/20250510091114.png)
- 新增岗位: ![](http://yc-imgs.oss-cn-shanghai.aliyuncs.com/img/20250510091314.png)

```mermaid
graph TD
A[岗位管理页面] --> B[添加岗位表单]
A --> C[编辑岗位表单]
A --> D[删除岗位确认]
A --> E[岗位搜索栏]
A --> F[岗位列表]
```

### （六）字典管理

1. **字典类型管理** ：创建和管理字典类型，如性别、状态、城市等。
2. **字典数据管理** ：在字典类型下添加、编辑、删除字典数据项，设置数据项的值和标签。
3. **字典查询与引用** ：方便在系统中其他模块查询和引用字典数据，实现数据的规范化管理。
4. **原型图**:

- 字典类型列表：![](https://yc-imgs.oss-cn-shanghai.aliyuncs.com/20250510215519326.png)
- 新增字典类型：![](https://yc-imgs.oss-cn-shanghai.aliyuncs.com/20250510215602949.png)
- 字典数据列表：![](https://yc-imgs.oss-cn-shanghai.aliyuncs.com/20250510215709187.png)
- 新增字典数据: ![](https://yc-imgs.oss-cn-shanghai.aliyuncs.com/20250510215745110.png)

```mermaid
graph TD
A[字典管理页面] --> B[字典类型列表]
A --> C[添加字典类型表单]
A --> D[字典数据列表]
A --> E[添加字典数据表单]
```

### （七）参数管理

1. **系统参数配置** ：对系统的公共参数进行配置和管理，如页面大小、缓存时间等。
2. **参数查询与修改** ：可按参数名称、参数键名等条件查询参数，并进行修改操作。
   **原型图**

```mermaid
graph TD
A[参数管理页面] --> B[参数搜索栏]
A --> C[参数列表]
A --> D[编辑参数表单]
```

### （八）通知公告

1. **公告发布** ：管理员可以发布通知公告，包括标题、内容、发布人、发布时间等信息。
2. **公告查询与管理** ：支持按标题、类型、状态等条件查询公告，对公告进行编辑、删除、上下线等操作。
   **原型图**

```mermaid
graph TD
A[通知公告页面] --> B[发布公告表单]
A --> C[公告搜索栏]
A --> D[公告列表]
A --> E[编辑公告表单]
```

### （九）日志管理

1. **登录日志记录** ：记录用户的登录信息，包括登录时间、登录 IP、登录状态等。
2. **操作日志记录** ：记录用户在系统中的操作行为，如添加、编辑、删除数据等操作的时间、模块、操作人等信息。
3. **日志查询与分析** ：提供日志查询功能，可按时间范围、用户、操作类型等条件进行筛选，方便对系统的运行情况进行监控和分析。
   **原型图**

```mermaid
graph TD
A[日志管理页面] --> B[日志搜索栏]
A --> C[日志列表]
A --> D[日志详情]
```

### （十）文件管理

1. **文件上传** ：支持多种文件类型上传，如图片、文档、压缩包等。
2. **文件存储与管理** ：对上传的文件进行存储和管理，提供文件列表展示、文件预览、文件下载、文件删除等功能。
3. **文件分类** ：可对文件进行分类存储和管理，方便用户查找和使用。
   **原型图**

```mermaid
graph TD
A[文件管理页面] --> B[文件上传按钮]
A --> C[文件列表]
A --> D[文件预览]
A --> E[文件下载]
```

### （十一）定时任务

1. **任务创建与配置** ：能够创建定时任务，设置任务名称、任务组、调用目标、cron 表达式等参数。
2. **任务状态管理** ：控制定时任务的启动、暂停、恢复、停止等状态。
3. **任务日志记录** ：记录定时任务的执行时间、执行结果、异常信息等日志，方便对任务的运行情况进行监控和排查。
   **原型图**

```mermaid
graph TD
A[定时任务页面] --> B[添加任务表单]
A --> C[任务列表]
A --> D[任务操作按钮组]
A --> E[任务日志]
```


## 四、非功能需求

### （一）性能需求

1. 系统响应时间：在正常负载下，页面加载时间不超过 3 秒，操作响应时间不超过 2 秒。
2. 并发处理能力：能够支持至少 1000 个并发用户访问，系统性能保持稳定。

### （二）安全性需求

1. 访问控制：采用基于角色的访问控制机制，确保用户只能访问其被授权的资源和功能。
2. 数据加密：对用户密码、敏感数据等进行加密存储和传输，保障数据的安全性。
3. 防止攻击：具备防止常见网络攻击的能力，如 SQL 注入、XSS 攻击、CSRF 攻击等。

### （三）易用性需求

1. 界面友好：系统界面简洁直观，操作流程清晰易懂，方便用户快速上手和使用。
2. 操作提示：提供完善的操作提示和引导信息，帮助用户顺利完成各项操作。

### （四）兼容性需求

1. 浏览器兼容：兼容主流浏览器，如 Chrome、Firefox、Safari、Edge 等，确保在不同浏览器上的显示和功能一致。
2. 移动端适配：在移动设备上能够正常访问和使用系统，提供良好的移动端体验。

### （五）可扩展性需求

1. 模块扩展：系统采用模块化设计，方便进行功能模块的扩展和定制开发。
2. 技术栈升级：能够支持技术栈的升级和更新，以适应不断发展的技术需求。

## 五、系统架构设计

### （一）总体架构

hx - frame 后台管理系统采用前后端分离的架构，后端基于 Spring Boot 框架，二者通过 RESTful API
进行通信。这种架构设计提高了系统的可维护性和可扩展性，方便前后端团队的独立开发和部署。

### （三）后端架构

1. Spring Boot：作为后端开发框架，简化了 Spring 应用的开发和配置，提供了自动配置、嵌入式服务器等特性。
2. Spring Security：用于实现系统的安全认证和授权功能，保障系统的安全性。
3. Spring Data JPA：作为持久层框架，简化了数据库操作，提高了开发效率。
4. Redis：用于缓存数据，提高系统的性能和响应速度。
5. Quartz：作为定时任务调度框架，实现系统的定时任务功能。

### （四）数据库设计

1. 数据库选择：采用 MySQL 数据库，具有性能稳定、易于维护、功能强大等特点，能够满足系统对数据存储和管理的需求。
2. 数据库表结构设计：根据系统的功能需求，设计合理的数据库表结构，包括用户表、角色表、菜单表、部门表、字典表等，确保数据的存储和查询效率。
3. 数据库设计文档 [db-design.md](../db/db-design.md)
