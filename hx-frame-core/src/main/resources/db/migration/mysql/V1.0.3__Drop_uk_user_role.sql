-- 为hx_frame_role_menu表添加缺少的通用字段
ALTER TABLE hx_frame_role_menu
    ADD COLUMN version INT NOT NULL DEFAULT 1 COMMENT '乐观锁版本号',
    ADD COLUMN updated_by VARCHAR(50) COMMENT '更新人',
    ADD COLUMN updated_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间';

-- 为hx_frame_user_role表添加缺少的通用字段
ALTER TABLE hx_frame_user_role
    ADD COLUMN version INT NOT NULL DEFAULT 1 COMMENT '乐观锁版本号',
    ADD COLUMN updated_by VARCHAR(50) COMMENT '更新人',
    ADD COLUMN updated_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间';

-- 更新已有记录的version字段
UPDATE hx_frame_role_menu SET version = 1 WHERE version IS NULL;
UPDATE hx_frame_user_role SET version = 1 WHERE version IS NULL;
