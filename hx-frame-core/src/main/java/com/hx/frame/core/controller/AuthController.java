package com.hx.frame.core.controller;

import com.hx.frame.commons.response.AuthResponse;
import com.hx.frame.commons.response.Result;
import com.hx.frame.core.dto.DtoMenu;
import com.hx.frame.core.security.UserContext;
import com.hx.frame.core.service.AuthService;
import com.hx.frame.core.vo.UserInfoVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 认证控制器
 * 提供用户认证相关的RESTful API接口，包括登录和令牌刷新功能
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-18
 */
@RestController
@RequestMapping("/api/auth")
public class AuthController {

    private final AuthService authService;

    public AuthController(AuthService authService) {
        this.authService = authService;
    }

    /**
     * 用户登录
     * 验证用户名和密码，生成访问令牌和刷新令牌
     *
     * @param username 用户名
     * @param password 密码
     * @return 包含访问令牌和刷新令牌的响应结果
     */
    @PostMapping("/login")
    public Result<AuthResponse> login(@RequestParam String username, @RequestParam String password) {
        return Result.success(authService.login(username, password));
    }

    /**
     * 刷新令牌
     * 使用有效的刷新令牌获取新的访问令牌
     *
     * @param refreshToken 刷新令牌
     * @return 包含新访问令牌的响应结果
     */
    @PostMapping("/refresh")
    public Result<AuthResponse> refresh(@RequestParam String refreshToken) {
        return Result.success(authService.refresh(refreshToken));
    }

    /**
     * 用户注销
     * 使当前令牌失效
     *
     * @return 操作结果
     */
    @PostMapping("/logout")
    public Result<Void> logout() {
        // 从SecurityContext中获取当前用户的token
        String token = UserContext.getCurrentToken();
        if (token != null) {
            authService.logout(token);
        }
        return Result.success();
    }

    /**
     * 获取当前用户信息
     *
     * @return 当前用户信息
     */
    @GetMapping("/info")
    public Result<UserInfoVO> getById() {
        // 调用服务层获取用户数据，包含角色信息
        UserInfoVO user = authService.loadCurrentUser();
        return Result.success(user);
    }

    /**
     * 获取当前用户的菜单树
     * 根据当前用户的角色权限，获取其可访问的菜单列表，并以树形结构返回
     *
     * @return 用户菜单树形结构
     */
    @GetMapping("/menus")
    public Result<List<DtoMenu>> getCurrentUserMenus() {
        // 调用服务层获取当前用户的菜单树
        List<DtoMenu> menus = authService.getCurrentUserMenus();
        return Result.success(menus);
    }
}
