package com.hx.frame.core.service.impl;

import com.hx.frame.commons.base.service.impl.BaseServiceImpl;
import com.hx.frame.commons.exception.BusinessException;
import com.hx.frame.core.dto.DtoRole;
import com.hx.frame.core.dto.DtoUserRole;
import com.hx.frame.core.repository.RoleRepository;
import com.hx.frame.core.repository.UserRepository;
import com.hx.frame.core.repository.UserRoleRepository;
import com.hx.frame.core.service.UserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户角色关联服务实现类
 * 实现用户角色关联相关的业务操作
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-20
 */
@Service
public class UserRoleServiceImpl extends BaseServiceImpl<DtoUserRole, UserRoleRepository> implements UserRoleService {

    /**
     * 用户仓库
     */
    private final UserRepository userRepository;

    /**
     * 角色仓库
     */
    private final RoleRepository roleRepository;

    /**
     * 构造函数
     *
     * @param repository 用户角色关联仓库
     * @param userRepository 用户仓库
     * @param roleRepository 角色仓库
     */
    @Autowired
    public UserRoleServiceImpl(UserRoleRepository repository, UserRepository userRepository, RoleRepository roleRepository) {
        super.setRepository(repository);
        this.userRepository = userRepository;
        this.roleRepository = roleRepository;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public void assignRoles(String userId, List<String> roleIds) {
        // 参数校验
        if (!StringUtils.hasText(userId)) {
            throw new BusinessException("用户ID不能为空");
        }

        // 查询用户是否存在
        if (!userRepository.existsById(userId)) {
            throw new BusinessException("用户不存在");
        }

        // 删除原有的用户角色关联
        deleteByUserId(userId);

        // 如果角色ID列表为空，则只删除原有关联
        if (CollectionUtils.isEmpty(roleIds)) {
            return;
        }

        // 创建新的用户角色关联
        List<DtoUserRole> newUserRoles = new ArrayList<>();
        for (String roleId : roleIds) {
            DtoUserRole userRole = new DtoUserRole();
            userRole.setUserId(userId);
            userRole.setRoleId(roleId);
            newUserRoles.add(userRole);
        }

        // 批量保存新的用户角色关联
        repository.saveAll(newUserRoles);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public List<String> getRoleIdsByUserId(String userId) {
        if (!StringUtils.hasText(userId)) {
            return new ArrayList<>();
        }

        // 查询用户角色关联
        List<DtoUserRole> userRoles = repository.findByUserId(userId);

        // 提取角色ID列表
        return userRoles.stream()
                .map(DtoUserRole::getRoleId)
                .collect(Collectors.toList());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public List<DtoRole> getRolesByUserId(String userId) {
        // 获取用户已分配的角色ID列表
        List<String> roleIds = getRoleIdsByUserId(userId);

        if (CollectionUtils.isEmpty(roleIds)) {
            return new ArrayList<>();
        }

        // 查询角色信息
        return roleRepository.findAllById(roleIds);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public void deleteByUserIds(List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        for (String userId : userIds) {
            deleteByUserId(userId);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public void deleteByRoleIds(List<String> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return;
        }

        for (String roleId : roleIds) {
            deleteByRoleId(roleId);
        }
    }

    /**
     * 根据用户ID删除用户角色关联
     *
     * @param userId 用户ID
     */
    private void deleteByUserId(String userId) {
        if (!StringUtils.hasText(userId)) {
            return;
        }

        List<DtoUserRole> userRoles = repository.findByUserId(userId);
        if (!CollectionUtils.isEmpty(userRoles)) {
            List<String> userRoleIds = userRoles.stream()
                    .map(DtoUserRole::getId)
                    .collect(Collectors.toList());
            super.logicDelete(userRoleIds);
        }
    }

    /**
     * 根据角色ID删除用户角色关联
     *
     * @param roleId 角色ID
     */
    private void deleteByRoleId(String roleId) {
        if (!StringUtils.hasText(roleId)) {
            return;
        }

        List<DtoUserRole> userRoles = repository.findByRoleId(roleId);
        if (!CollectionUtils.isEmpty(userRoles)) {
            List<String> userRoleIds = userRoles.stream()
                    .map(DtoUserRole::getId)
                    .collect(Collectors.toList());
            super.logicDelete(userRoleIds);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public DtoUserRole findByUserIdAndRoleId(String userId, String roleId) {
        if (!StringUtils.hasText(userId) || !StringUtils.hasText(roleId)) {
            return null;
        }
        return repository.findByUserIdAndRoleId(userId, roleId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public List<DtoUserRole> findByRoleId(String roleId) {
        if (!StringUtils.hasText(roleId)) {
            return new ArrayList<>();
        }
        return repository.findByRoleId(roleId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public List<DtoUserRole> saveAll(List<DtoUserRole> userRoles) {
        if (userRoles == null || userRoles.isEmpty()) {
            return new ArrayList<>();
        }
        return repository.saveAll(userRoles);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public List<DtoUserRole> updateAll(List<DtoUserRole> userRoles) {
        if (userRoles == null || userRoles.isEmpty()) {
            return new ArrayList<>();
        }

        List<DtoUserRole> updatedUserRoles = new ArrayList<>();
        for (DtoUserRole userRole : userRoles) {
            DtoUserRole updated = super.update(userRole);
            updatedUserRoles.add(updated);
        }
        return updatedUserRoles;
    }
}
