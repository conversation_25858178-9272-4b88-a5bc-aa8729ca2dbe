package com.hx.frame.core.controller;

import com.hx.frame.commons.base.controller.BaseController;
import com.hx.frame.commons.response.Result;
import com.hx.frame.core.condition.UserQueryCondition;
import com.hx.frame.core.dto.DtoRole;
import com.hx.frame.core.dto.DtoUser;
import com.hx.frame.core.service.UserService;
import com.hx.frame.core.vo.UserInfoVO;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * 用户控制器
 * 提供用户管理相关的RESTful API接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-18
 */
@RestController
@RequestMapping("/api/users")
public class UserController extends BaseController<DtoUser, UserService> {

    /**
     * 创建用户
     * 创建新的用户记录
     *
     * @param user 用户数据对象
     * @return 创建成功的用户对象
     */
    @PostMapping
    public Result<UserInfoVO> create(@RequestBody DtoUser user) {
        // 调用服务层保存用户数据
        return Result.success(service.convertToUserInfoVO(service.save(user)));
    }

    /**
     * 更新用户
     * 更新现有的用户记录
     *
     * @param user 用户数据对象（包含ID）
     * @return 更新后的用户对象
     */
    @PutMapping
    public Result<UserInfoVO> update(@RequestBody DtoUser user) {
        // 调用服务层更新用户数据
        return Result.success(service.convertToUserInfoVO(service.update(user)));
    }

    /**
     * 根据ID获取用户
     * 获取指定ID的用户详细信息
     *
     * @param id 用户ID
     * @return 用户对象
     */
    @GetMapping("/{id}")
    public Result<UserInfoVO> getById(@PathVariable("id") String id) {
        // 调用服务层获取用户数据，包含角色信息
        DtoUser user = service.getUserWithRoles(id);
        return Result.success(service.convertToUserInfoVO(user));
    }

    /**
     * 批量删除用户
     * 根据ID列表批量删除用户
     *
     * @param ids 用户ID列表
     * @return 操作结果
     */
    @DeleteMapping
    public Result<Void> delete(@RequestBody Collection<String> ids) {
        // 调用服务层执行逻辑删除
        service.logicDelete(ids);
        return Result.success();
    }

    /**
     * 用户查询接口
     *
     * @param condition 查询条件
     * @return 用户分页结果
     */
    @PostMapping("/list")
    public ResponseEntity<Page<UserInfoVO>> query(@RequestBody UserQueryCondition condition) {
        // 直接调用基础服务层的查询方法
        Page<DtoUser> result = service.findByCondition(condition);
        result.getContent().forEach(user -> user.setPassword(null));
        return ResponseEntity.ok(result);
    }

    /**
     * 重置用户密码
     *
     * @param userId 用户ID
     * @return 操作结果
     */
    @PostMapping("/{userId}/reset-password")
    public Result<Void> resetPassword(@PathVariable("userId") String userId) {
        service.resetPassword(userId);
        return Result.success();
    }

    /**
     * 获取用户已分配的角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    @GetMapping("/{userId}/roles")
    public Result<List<DtoRole>> getUserRoles(@PathVariable("userId") String userId) {
        List<DtoRole> roles = service.getRolesByUserId(userId);
        return Result.success(roles);
    }

    /**
     * 为用户分配角色
     *
     * @param userId  用户ID
     * @param roleIds 角色ID列表
     * @return 操作结果
     */
    @PostMapping("/{userId}/roles")
    public Result<Void> assignRoles(@PathVariable("userId") String userId, @RequestBody List<String> roleIds) {
        service.assignRoles(userId, roleIds);
        return Result.success();
    }

    /**
     * 查询未分配指定角色的用户列表
     *
     * @param roleId 角色ID
     * @return 未分配角色的用户列表
     */
    @GetMapping("/without-roles/{roleId}")
    public Result<List<DtoUser>> getUsersWithoutRoles(@PathVariable String roleId) {
        List<DtoUser> users = service.getUsersWithoutRoles(roleId);
        users.forEach(user -> user.setPassword(null));
        return Result.success(users);
    }
}
