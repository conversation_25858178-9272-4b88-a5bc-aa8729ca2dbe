package com.hx.frame.core.vo;

import com.hx.frame.core.dto.DtoRole;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;


/**
 * 用户信息视图对象
 * 用于封装用户信息，包括用户基本信息和角色信息
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-20
 */
@Data
@AllArgsConstructor
@Builder
public class UserInfoVO {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String phone;

    /**
     * 状态
     */
    private String status;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 岗位ID
     */
    private String postId;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 角色编码集合
     */
    private List<String> roleCodes;

    /**
     * 角色列表
     */
    private List<DtoRole> roleList;
}
