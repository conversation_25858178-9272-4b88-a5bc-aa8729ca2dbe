package com.hx.frame.core.service.impl;

import com.hx.frame.commons.base.service.impl.BaseServiceImpl;
import com.hx.frame.commons.exception.BusinessException;
import com.hx.frame.core.dto.DtoMenu;
import com.hx.frame.core.dto.DtoRole;
import com.hx.frame.core.dto.DtoRoleMenu;
import com.hx.frame.core.repository.MenuRepository;
import com.hx.frame.core.repository.RoleMenuRepository;
import com.hx.frame.core.repository.RoleRepository;
import com.hx.frame.core.service.RoleMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色菜单关联服务实现类
 * 实现角色菜单关联相关的业务操作
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-20
 */
@Service
public class RoleMenuServiceImpl extends BaseServiceImpl<DtoRoleMenu, RoleMenuRepository> implements RoleMenuService {

    /**
     * 角色仓库
     */
    private final RoleRepository roleRepository;

    /**
     * 菜单仓库
     */
    private final MenuRepository menuRepository;

    /**
     * 构造函数
     *
     * @param repository 角色菜单关联仓库
     * @param roleRepository 角色仓库
     * @param menuRepository 菜单仓库
     */
    @Autowired
    public RoleMenuServiceImpl(RoleMenuRepository repository, RoleRepository roleRepository, MenuRepository menuRepository) {
        super.setRepository(repository);
        this.roleRepository = roleRepository;
        this.menuRepository = menuRepository;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public void assignMenus(String roleId, List<String> menuIds) {
        // 参数校验
        if (!StringUtils.hasText(roleId)) {
            throw new BusinessException("角色ID不能为空");
        }

        // 查询角色是否存在
        DtoRole role = roleRepository.findById(roleId).orElse(null);
        if (role == null) {
            throw new BusinessException("角色不存在");
        }

        // 删除原有的角色菜单关联
        deleteByRoleId(roleId);

        // 如果菜单ID列表为空，则只删除原有关联
        if (CollectionUtils.isEmpty(menuIds)) {
            return;
        }

        // 创建新的角色菜单关联
        List<DtoRoleMenu> newRoleMenus = new ArrayList<>();
        for (String menuId : menuIds) {
            DtoRoleMenu roleMenu = new DtoRoleMenu();
            roleMenu.setRoleId(roleId);
            roleMenu.setMenuId(menuId);
            newRoleMenus.add(roleMenu);
        }

        // 批量保存新的角色菜单关联
        repository.saveAll(newRoleMenus);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public List<String> getMenuIdsByRoleId(String roleId) {
        if (!StringUtils.hasText(roleId)) {
            return new ArrayList<>();
        }

        // 查询角色菜单关联
        List<DtoRoleMenu> roleMenus = repository.findByRoleId(roleId);

        // 提取菜单ID列表
        return roleMenus.stream()
                .map(DtoRoleMenu::getMenuId)
                .collect(Collectors.toList());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public List<DtoMenu> getMenusByRoleId(String roleId) {
        // 获取角色已分配的菜单ID列表
        List<String> menuIds = getMenuIdsByRoleId(roleId);

        if (CollectionUtils.isEmpty(menuIds)) {
            return new ArrayList<>();
        }

        // 查询菜单信息
        return menuRepository.findAllById(menuIds);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public void deleteByRoleIds(List<String> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return;
        }

        for (String roleId : roleIds) {
            deleteByRoleId(roleId);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public void deleteByMenuIds(Collection<String> menuIds) {
        if (CollectionUtils.isEmpty(menuIds)) {
            return;
        }

        for (String menuId : menuIds) {
            deleteByMenuId(menuId);
        }
    }

    /**
     * 根据角色ID删除角色菜单关联
     *
     * @param roleId 角色ID
     */
    private void deleteByRoleId(String roleId) {
        if (!StringUtils.hasText(roleId)) {
            return;
        }

        List<DtoRoleMenu> roleMenus = repository.findByRoleId(roleId);
        if (!CollectionUtils.isEmpty(roleMenus)) {
            List<String> roleMenuIds = roleMenus.stream()
                    .map(DtoRoleMenu::getId)
                    .collect(Collectors.toList());
            super.logicDelete(roleMenuIds);
        }
    }

    /**
     * 根据菜单ID删除角色菜单关联
     *
     * @param menuId 菜单ID
     */
    private void deleteByMenuId(String menuId) {
        if (!StringUtils.hasText(menuId)) {
            return;
        }

        List<DtoRoleMenu> roleMenus = repository.findByMenuId(menuId);
        if (!CollectionUtils.isEmpty(roleMenus)) {
            List<String> roleMenuIds = roleMenus.stream()
                    .map(DtoRoleMenu::getId)
                    .collect(Collectors.toList());
            super.logicDelete(roleMenuIds);
        }
    }
}
