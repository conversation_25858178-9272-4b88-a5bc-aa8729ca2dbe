package com.hx.frame.core.service.impl;

import com.hx.frame.commons.base.service.impl.BaseServiceImpl;
import com.hx.frame.commons.constant.UserConstants;
import com.hx.frame.commons.exception.BusinessException;
import com.hx.frame.core.dto.DtoRole;
import com.hx.frame.core.dto.DtoUser;
import com.hx.frame.core.repository.UserRepository;
import com.hx.frame.core.service.UserRoleService;
import com.hx.frame.core.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户服务实现类
 * 实现用户相关的业务操作
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-18
 */
@Service
public class UserServiceImpl extends BaseServiceImpl<DtoUser, UserRepository> implements UserService {

    /**
     * 密码编码器
     */
    private final PasswordEncoder passwordEncoder;

    /**
     * 用户角色关联服务
     */
    private final UserRoleService userRoleService;

    // 默认密码已移动到 UserConstants 类中

    /**
     * 构造函数
     *
     * @param repository 用户仓库
     * @param passwordEncoder 密码编码器
     * @param userRoleService 用户角色关联服务
     */
    @Autowired
    public UserServiceImpl(UserRepository repository, PasswordEncoder passwordEncoder, UserRoleService userRoleService) {
        super.setRepository(repository);
        this.passwordEncoder = passwordEncoder;
        this.userRoleService = userRoleService;
    }

    /**
     * 重写保存方法，处理角色分配
     *
     * @param user 用户实体
     * @return 保存后的用户实体
     */
    @Override
    @Transactional
    public DtoUser save(DtoUser user) {
        // 调用父类的保存方法
        DtoUser savedUser = super.save(user);

        // 处理角色分配
        if (user.getRoleIds() != null && !user.getRoleIds().isEmpty()) {
            userRoleService.assignRoles(savedUser.getId(), user.getRoleIds());
        }

        return savedUser;
    }

    /**
     * 重写更新方法，处理角色分配
     *
     * @param user 用户实体
     * @return 更新后的用户实体
     */
    @Override
    @Transactional
    public DtoUser update(DtoUser user) {
        // 调用父类的更新方法
        DtoUser updatedUser = super.update(user);

        // 处理角色分配
        userRoleService.assignRoles(updatedUser.getId(), user.getRoleIds());

        return updatedUser;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public void resetPassword(String userId) {
        // 查询用户
        DtoUser user = findOne(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 重置密码
        user.setPassword(passwordEncoder.encode(UserConstants.DEFAULT_PASSWORD));

        // 更新用户
        update(user);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public void logicDelete(Collection<String> ids) {
        // 调用父类的逻辑删除方法
        super.logicDelete(ids);

        // 删除用户角色关联
        userRoleService.deleteByUserIds(new ArrayList<>(ids));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public DtoUser getUserWithRoles(String id) {
        // 获取用户基本信息
        DtoUser user = findOne(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 获取用户已分配的角色ID列表
        List<String> roleIds = userRoleService.getRoleIdsByUserId(id);
        user.setRoleIds(roleIds);

        // 获取用户已分配的角色列表
        List<DtoRole> roles = userRoleService.getRolesByUserId(id);
        user.setRoles(roles);

        // 设置角色名称列表
        List<String> roleNames = roles.stream()
                .map(DtoRole::getRoleName)
                .collect(Collectors.toList());
        user.setRoleNames(roleNames);

        return user;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public void assignRoles(String userId, List<String> roleIds) {
        // 参数校验
        if (userId == null || userId.trim().isEmpty()) {
            throw new BusinessException("用户ID不能为空");
        }

        // 查询用户是否存在
        DtoUser user = findOne(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 调用用户角色关联服务分配角色
        userRoleService.assignRoles(userId, roleIds);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public List<DtoRole> getRolesByUserId(String userId) {
        // 参数校验
        if (userId == null || userId.trim().isEmpty()) {
            return new ArrayList<>();
        }

        // 查询用户是否存在
        DtoUser user = findOne(userId);
        if (user == null) {
            return new ArrayList<>();
        }

        // 调用用户角色关联服务获取角色列表
        return userRoleService.getRolesByUserId(userId);
    }
}