package com.hx.frame.core.security;

import com.hx.frame.commons.constant.CommonConstants;
import com.hx.frame.commons.util.SpringContextHolder;
import com.hx.frame.core.dto.DtoUser;
import com.hx.frame.core.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 用户上下文工具类
 * 用于获取当前登录用户信息和认证令牌
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-27
 */
@Slf4j
public class UserContext {

    private UserContext() {
        throw new IllegalStateException("工具类不允许实例化");
    }

    /**
     * 获取当前登录用户名
     *
     * @return 当前登录用户名，如果未登录则返回null
     */
    public static String getCurrentUsername() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof UserDetails) {
            return ((UserDetails) authentication.getPrincipal()).getUsername();
        }
        return null;
    }

    /**
     * 获取当前用户的JWT令牌
     *
     * @return JWT令牌字符串，如果未找到则返回null
     */
    public static String getCurrentToken() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            String authHeader = request.getHeader(CommonConstants.Http.AUTHORIZATION_HEADER);
            if (authHeader != null && authHeader.startsWith(CommonConstants.Http.BEARER_PREFIX)) {
                return authHeader.substring(CommonConstants.Http.BEARER_PREFIX.length());
            }
        }
        return null;
    }

    /**
     * 获取当前登录用户的认证信息
     *
     * @return Authentication对象，如果未登录则返回null
     */
    public static Authentication getCurrentAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    /**
     * 判断当前用户是否已认证
     *
     * @return 如果用户已认证则返回true，否则返回false
     */
    public static boolean isAuthenticated() {
        Authentication authentication = getCurrentAuthentication();
        return authentication != null && authentication.isAuthenticated();
    }

    /**
     * 判断当前用户是否具有管理员角色
     *
     * @return 如果用户具有管理员角色则返回true，否则返回false
     */
    public static boolean isAdmin() {
        Authentication authentication = getCurrentAuthentication();
        return authentication != null && authentication.getAuthorities().stream()
                .anyMatch(authority -> CommonConstants.Security.ROLE_ADMIN.equals(authority.getAuthority()));
    }

    /**
     * 获取当前用户ID
     * 直接从认证对象中获取，避免查询数据库
     *
     * @return 用户ID，如果未找到则返回null
     */
    public static String getCurrentUserId() {
        return getCurrentUser().getId();
    }

    /**
     * 获取当前用户信息
     *
     * @return 当前用户信息
     */
    public static DtoUser getCurrentUser() {
        String userName = getCurrentUsername();
        UserRepository userRepository = SpringContextHolder.getBean(UserRepository.class);
        return userRepository.findByUsername(userName);
    }
}