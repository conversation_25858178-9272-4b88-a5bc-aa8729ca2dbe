package com.hx.frame.core.service;

import com.hx.frame.commons.base.service.IBaseService;
import com.hx.frame.core.dto.DtoMenu;
import com.hx.frame.core.dto.DtoRoleMenu;

import java.util.Collection;
import java.util.List;

/**
 * 角色菜单关联服务接口
 * 定义角色菜单关联相关的业务操作
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-20
 */
public interface RoleMenuService extends IBaseService<DtoRoleMenu> {

    /**
     * 为角色分配菜单权限
     *
     * @param roleId  角色ID
     * @param menuIds 菜单ID列表
     */
    void assignMenus(String roleId, List<String> menuIds);

    /**
     * 获取角色已分配的菜单ID列表
     *
     * @param roleId 角色ID
     * @return 菜单ID列表
     */
    List<String> getMenuIdsByRoleId(String roleId);

    /**
     * 获取角色已分配的菜单列表
     *
     * @param roleId 角色ID
     * @return 菜单列表
     */
    List<DtoMenu> getMenusByRoleId(String roleId);

    /**
     * 获取角色已分配的菜单列表
     *
     * @param roleIds 角色ID集合
     * @return 菜单列表
     */
    List<DtoMenu> getMenusByRoleIds(Collection<String> roleIds);

    /**
     * 根据角色ID列表批量删除角色菜单关联
     *
     * @param roleIds 角色ID列表
     */
    void deleteByRoleIds(List<String> roleIds);

    /**
     * 根据菜单ID列表批量删除角色菜单关联
     *
     * @param menuIds 菜单ID列表
     */
    void deleteByMenuIds(Collection<String> menuIds);
}
