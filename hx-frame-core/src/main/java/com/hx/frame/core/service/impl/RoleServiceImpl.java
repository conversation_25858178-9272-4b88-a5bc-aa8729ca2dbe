package com.hx.frame.core.service.impl;

import com.hx.frame.commons.base.service.impl.BaseServiceImpl;
import com.hx.frame.commons.exception.BusinessException;
import com.hx.frame.core.dto.DtoMenu;
import com.hx.frame.core.dto.DtoRole;
import com.hx.frame.core.dto.DtoUser;
import com.hx.frame.core.dto.DtoUserRole;
import com.hx.frame.core.repository.RoleRepository;
import com.hx.frame.core.repository.UserRepository;
import com.hx.frame.core.service.RoleMenuService;
import com.hx.frame.core.service.RoleService;
import com.hx.frame.core.service.UserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 角色服务实现类
 * 实现角色相关的业务操作
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-15
 */
@Service
public class RoleServiceImpl extends BaseServiceImpl<DtoRole, RoleRepository> implements RoleService {

    /**
     * 角色菜单关联服务
     */
    private final RoleMenuService roleMenuService;

    /**
     * 用户角色关联服务
     */
    private final UserRoleService userRoleService;

    /**
     * 用户仓库
     */
    private final UserRepository userRepository;

    /**
     * 构造函数
     *
     * @param repository 角色仓库
     * @param roleMenuService 角色菜单关联服务
     * @param userRoleService 用户角色关联服务
     * @param userRepository 用户仓库
     */
    @Autowired
    public RoleServiceImpl(RoleRepository repository, RoleMenuService roleMenuService, UserRoleService userRoleService, UserRepository userRepository) {
        super.setRepository(repository);
        this.roleMenuService = roleMenuService;
        this.userRoleService = userRoleService;
        this.userRepository = userRepository;
    }

    /**
     * 重写保存方法，添加角色名称和权限字符串唯一性检查，并处理菜单权限分配
     *
     * @param role 角色实体
     * @return 保存后的角色实体
     */
    @Override
    @Transactional
    public DtoRole save(DtoRole role) {
        // 检查角色名称是否已存在
        if (checkRoleNameExists(role.getRoleName(), null)) {
            throw new BusinessException("角色名称已存在");
        }

        // 检查角色权限字符串是否已存在
        if (checkRoleKeyExists(role.getRoleKey(), null)) {
            throw new BusinessException("角色权限字符已存在");
        }

        // 调用父类的保存方法
        DtoRole savedRole = super.save(role);

        // 处理菜单权限分配
        if (role.getMenuIds() != null && !role.getMenuIds().isEmpty()) {
            roleMenuService.assignMenus(savedRole.getId(), role.getMenuIds());
        }

        return savedRole;
    }

    /**
     * 重写更新方法，添加角色名称和权限字符串唯一性检查，并处理菜单权限分配
     *
     * @param role 角色实体
     * @return 更新后的角色实体
     */
    @Override
    @Transactional
    public DtoRole update(DtoRole role) {
        // 检查角色名称是否已存在
        if (checkRoleNameExists(role.getRoleName(), role.getId())) {
            throw new BusinessException("角色名称已存在");
        }

        // 检查角色权限字符串是否已存在
        if (checkRoleKeyExists(role.getRoleKey(), role.getId())) {
            throw new BusinessException("角色权限字符串已存在");
        }

        // 调用父类的更新方法
        DtoRole updatedRole = super.update(role);

        // 处理菜单权限分配
        roleMenuService.assignMenus(updatedRole.getId(), role.getMenuIds());

        return updatedRole;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public DtoRole getRoleWithMenus(String id) {
        // 获取角色基本信息
        DtoRole role = findOne(id);

        // 获取角色已分配的菜单列表
        List<DtoMenu> menus = roleMenuService.getMenusByRoleId(id);
        role.setMenus(menus);
        role.setMenuIds(menus.stream().map(DtoMenu::getId).collect(Collectors.toList()));

        return role;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public void logicDelete(Collection<String> ids) {
        // 删除角色菜单关联
        roleMenuService.deleteByRoleIds(new ArrayList<>(ids));

        // 删除用户角色关联
        userRoleService.deleteByRoleIds(new ArrayList<>(ids));

        // 调用父类的逻辑删除方法
        super.logicDelete(ids);
    }

    /**
     * 检查角色名称是否已存在
     *
     * @param roleName 角色名称
     * @param id       角色ID（更新时使用）
     * @return 是否存在
     */
    private boolean checkRoleNameExists(String roleName, String id) {
        DtoRole existingRole = repository.findByRoleName(roleName);
        if (existingRole == null) {
            return false;
        }

        // 如果是更新操作，且找到的是当前角色，则不算重复
        return !StringUtils.hasText(id) || !existingRole.getId().equals(id);
    }

    /**
     * 检查角色权限字符串是否已存在
     *
     * @param roleKey 角色权限字符串
     * @param id      角色ID（更新时使用）
     * @return 是否存在
     */
    private boolean checkRoleKeyExists(String roleKey, String id) {
        DtoRole existingRole = repository.findByRoleKey(roleKey);
        if (existingRole == null) {
            return false;
        }

        // 如果是更新操作，且找到的是当前角色，则不算重复
        return !StringUtils.hasText(id) || !existingRole.getId().equals(id);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public void assignUsers(String roleId, List<String> userIds) {
        // 参数校验
        if (!StringUtils.hasText(roleId)) {
            throw new BusinessException("角色ID不能为空");
        }

        // 查询角色是否存在
        DtoRole role = findOne(roleId);
        if (role == null) {
            throw new BusinessException("角色不存在");
        }

        // 如果用户ID列表为空，则直接返回
        if (userIds == null || userIds.isEmpty()) {
            return;
        }

        // 验证用户是否存在
        List<DtoUser> users = userRepository.findAllById(userIds);
        if (users.size() != userIds.size()) {
            throw new BusinessException("部分用户不存在");
        }

        // 批量查询现有的用户角色关联
        List<DtoUserRole> existingUserRoles = userRoleService.findByRoleId(roleId)
                .stream()
                .filter(userRole -> userIds.contains(userRole.getUserId()))
                .collect(Collectors.toList());

        // 创建现有关联的映射，便于快速查找
        Map<String, DtoUserRole> existingUserRoleMap = existingUserRoles.stream()
                .collect(Collectors.toMap(DtoUserRole::getUserId, userRole -> userRole));

        // 准备需要新增的用户角色关联列表
        List<DtoUserRole> newUserRoles = new ArrayList<>();
        // 准备需要恢复的用户角色关联列表
        List<DtoUserRole> restoreUserRoles = new ArrayList<>();

        // 处理每个用户ID
        for (String userId : userIds) {
            DtoUserRole existingUserRole = existingUserRoleMap.get(userId);

            if (existingUserRole == null) {
                // 如果关联不存在，则创建新的关联
                DtoUserRole newUserRole = new DtoUserRole();
                newUserRole.setUserId(userId);
                newUserRole.setRoleId(roleId);
                newUserRoles.add(newUserRole);
            } else if (existingUserRole.getDeleted()) {
                // 如果关联已删除，则恢复
                existingUserRole.setDeleted(false);
                restoreUserRoles.add(existingUserRole);
            }
            // 如果关联存在且未删除，则无需处理
        }

        // 批量保存新的用户角色关联
        if (!newUserRoles.isEmpty()) {
            userRoleService.saveAll(newUserRoles);
        }

        // 批量更新需要恢复的用户角色关联
        if (!restoreUserRoles.isEmpty()) {
            userRoleService.updateAll(restoreUserRoles);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public void removeUsers(String roleId, List<String> userIds) {
        // 参数校验
        if (!StringUtils.hasText(roleId)) {
            throw new BusinessException("角色ID不能为空");
        }

        // 查询角色是否存在
        DtoRole role = findOne(roleId);
        if (role == null) {
            throw new BusinessException("角色不存在");
        }

        // 如果用户ID列表为空，则直接返回
        if (userIds == null || userIds.isEmpty()) {
            return;
        }

        // 查询用户角色关联
        List<DtoUserRole> userRoles = new ArrayList<>();
        for (String userId : userIds) {
            DtoUserRole userRole = userRoleService.findByUserIdAndRoleId(userId, roleId);
            if (userRole != null && !userRole.getDeleted()) {
                userRoles.add(userRole);
            }
        }

        // 如果没有找到用户角色关联，则直接返回
        if (userRoles.isEmpty()) {
            return;
        }

        // 批量删除用户角色关联
        List<String> userRoleIds = userRoles.stream()
                .map(DtoUserRole::getId)
                .collect(Collectors.toList());
        userRoleService.logicDelete(userRoleIds);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public List<DtoUser> getUsersByRoleId(String roleId) {
        // 参数校验
        if (!StringUtils.hasText(roleId)) {
            return new ArrayList<>();
        }

        // 查询角色是否存在
        DtoRole role = findOne(roleId);
        if (role == null) {
            return new ArrayList<>();
        }

        // 查询用户角色关联
        List<DtoUserRole> userRoles = userRoleService.findByRoleId(roleId);
        if (userRoles.isEmpty()) {
            return new ArrayList<>();
        }

        // 提取用户ID列表
        List<String> userIds = userRoles.stream()
                .map(DtoUserRole::getUserId)
                .collect(Collectors.toList());

        // 查询用户信息
        return userRepository.findAllById(userIds);
    }
}
