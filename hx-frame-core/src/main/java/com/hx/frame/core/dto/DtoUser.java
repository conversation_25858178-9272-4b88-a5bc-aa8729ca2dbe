package com.hx.frame.core.dto;

import com.hx.frame.core.entity.User;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户数据传输对象
 * 用于与前端交互的用户数据对象，继承自User实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-18
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "hx_frame_user", uniqueConstraints = {
        @UniqueConstraint(columnNames = "username")
})
@Where(clause = " deleted = 0 ")
@Data
@DynamicInsert
public class DtoUser extends User {

    /**
     * 部门名称
     */
    @Transient
    private String departmentName;

    /**
     * 角色ID列表，用于接收前端传递的角色数据
     */
    @Transient
    private List<String> roleIds = new ArrayList<>();

    /**
     * 角色名称列表
     */
    @Transient
    private List<String> roleNames = new ArrayList<>();

    /**
     * 角色列表，用于存储用户已分配的角色
     */
    @Transient
    private List<DtoRole> roles = new ArrayList<>();
}