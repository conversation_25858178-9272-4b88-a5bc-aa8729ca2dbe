package com.hx.frame.core.service;

import com.hx.frame.commons.base.service.IBaseService;
import com.hx.frame.core.dto.DtoRole;
import com.hx.frame.core.dto.DtoUserRole;

import java.util.List;

/**
 * 用户角色关联服务接口
 * 定义用户角色关联相关的业务操作
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-20
 */
public interface UserRoleService extends IBaseService<DtoUserRole> {

    /**
     * 为用户分配角色
     *
     * @param userId  用户ID
     * @param roleIds 角色ID列表
     */
    void assignRoles(String userId, List<String> roleIds);

    /**
     * 获取用户已分配的角色ID列表
     *
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<String> getRoleIdsByUserId(String userId);

    /**
     * 获取用户已分配的角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<DtoRole> getRolesByUserId(String userId);

    /**
     * 根据用户ID列表批量删除用户角色关联
     *
     * @param userIds 用户ID列表
     */
    void deleteByUserIds(List<String> userIds);

    /**
     * 根据角色ID列表批量删除用户角色关联
     *
     * @param roleIds 角色ID列表
     */
    void deleteByRoleIds(List<String> roleIds);

    /**
     * 根据用户ID和角色ID查询用户角色关联
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 用户角色关联
     */
    DtoUserRole findByUserIdAndRoleId(String userId, String roleId);

    /**
     * 根据角色ID查询用户角色关联列表
     *
     * @param roleId 角色ID
     * @return 用户角色关联列表
     */
    List<DtoUserRole> findByRoleId(String roleId);

    /**
     * 批量保存用户角色关联
     *
     * @param userRoles 用户角色关联列表
     * @return 保存后的用户角色关联列表
     */
    List<DtoUserRole> saveAll(List<DtoUserRole> userRoles);

    /**
     * 批量更新用户角色关联
     *
     * @param userRoles 用户角色关联列表
     * @return 更新后的用户角色关联列表
     */
    List<DtoUserRole> updateAll(List<DtoUserRole> userRoles);
}
