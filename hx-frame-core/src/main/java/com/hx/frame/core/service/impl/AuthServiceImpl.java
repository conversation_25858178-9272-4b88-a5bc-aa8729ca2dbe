package com.hx.frame.core.service.impl;

import com.hx.frame.commons.constant.RedisConstants;
import com.hx.frame.commons.exception.BusinessException;
import com.hx.frame.commons.response.AuthResponse;
import com.hx.frame.core.config.JwtConfig;
import com.hx.frame.core.dto.DtoUser;
import com.hx.frame.core.security.UserContext;
import com.hx.frame.core.service.AuthService;
import com.hx.frame.core.service.UserService;
import com.hx.frame.util.JwtUtils;
import com.hx.frame.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 认证服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-18
 */
@Slf4j
@Service
public class AuthServiceImpl implements AuthService {

    private final AuthenticationManager authenticationManager;
    private final UserDetailsService userDetailsService;
    private final RedisUtils redisUtils;
    private final JwtConfig jwtConfig;
    private final UserService userService;

    public AuthServiceImpl(AuthenticationManager authenticationManager,
                           UserDetailsService userDetailsService,
                           RedisUtils redisUtils,
                           JwtConfig jwtConfig,
                           UserService userService) {
        this.authenticationManager = authenticationManager;
        this.userDetailsService = userDetailsService;
        this.redisUtils = redisUtils;
        this.jwtConfig = jwtConfig;
        this.userService = userService;
    }

    @Override
    public AuthResponse login(String username, String password) {
        try {
            log.debug("用户开始登录: {}", username);

            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(username, password)
            );

            UserDetails userDetails = (UserDetails) authentication.getPrincipal();
            log.debug("用户登录成功: {}", username);

            String accessToken = JwtUtils.generateAccessToken(userDetails);
            String refreshToken = JwtUtils.generateRefreshToken(userDetails);

            // 将刷新令牌存储到Redis
            String refreshTokenKey = RedisConstants.REFRESH_TOKEN_PREFIX + username;
            redisUtils.set(refreshTokenKey, refreshToken, jwtConfig.getRefreshTokenValidity(), TimeUnit.MINUTES);

            // 将访问令牌也存储到Redis
            String accessTokenKey = RedisConstants.ACCESS_TOKEN_PREFIX + username;
            redisUtils.set(accessTokenKey, accessToken, jwtConfig.getAccessTokenValidity(), TimeUnit.MINUTES);

            return new AuthResponse(accessToken, refreshToken, JwtUtils.TOKEN_TYPE_BEARER);
        } catch (AuthenticationException e) {
            log.error("用户登录失败: {}", username);
            throw new BusinessException("用户名或密码错误");
        }
    }

    @Override
    public AuthResponse refresh(String refreshToken) {
        String username = JwtUtils.extractUsername(refreshToken);
        if (username == null) {
            throw new BusinessException("无效的刷新令牌");
        }

        String refreshTokenKey = RedisConstants.REFRESH_TOKEN_PREFIX + username;
        String storedRefreshToken = redisUtils.get(refreshTokenKey);

        if (storedRefreshToken == null || !storedRefreshToken.equals(refreshToken)) {
            throw new BusinessException("刷新令牌已失效");
        }

        UserDetails userDetails = userDetailsService.loadUserByUsername(username);
        String newAccessToken = JwtUtils.generateAccessToken(userDetails);

        // 更新刷新令牌的过期时间
        redisUtils.expire(refreshTokenKey, jwtConfig.getRefreshTokenValidity(), TimeUnit.MINUTES);

        // 更新Redis中的访问令牌
        String accessTokenKey = RedisConstants.ACCESS_TOKEN_PREFIX + username;
        redisUtils.set(accessTokenKey, newAccessToken, jwtConfig.getAccessTokenValidity(), TimeUnit.MINUTES);

        return new AuthResponse(newAccessToken, refreshToken, JwtUtils.TOKEN_TYPE_BEARER);
    }

    @Override
    public void logout(String token) {
        // 从令牌中提取用户名
        String username = JwtUtils.extractUsername(token);
        if (username == null) {
            log.warn("注销失败：无效的令牌");
            return;
        }

        // 删除Redis中存储的刷新令牌
        String refreshTokenKey = RedisConstants.REFRESH_TOKEN_PREFIX + username;
        redisUtils.delete(refreshTokenKey);

        // 删除Redis中存储的访问令牌
        String accessTokenKey = RedisConstants.ACCESS_TOKEN_PREFIX + username;
        redisUtils.delete(accessTokenKey);

        log.info("用户 {} 已成功注销", username);
    }

    @Override
    public DtoUser loadCurrentUser() {
            return userService.getUserWithRoles(UserContext.getCurrentUserId());
    }
}
