package com.hx.frame.core.service;

import com.hx.frame.commons.response.AuthResponse;
import com.hx.frame.core.dto.DtoMenu;
import com.hx.frame.core.vo.UserInfoVO;

import java.util.List;

/**
 * 认证服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-18
 */
public interface AuthService {
    /**
     * 用户登录
     *
     * @param username 用户名
     * @param password 密码
     * @return 认证响应信息
     */
    AuthResponse login(String username, String password);

    /**
     * 刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return 认证响应信息
     */
    AuthResponse refresh(String refreshToken);

    /**
     * 用户注销
     * 使令牌失效
     *
     * @param token 访问令牌
     */
    void logout(String token);

    /**
     * 获取当前用户信息
     *
     * @return 当前用户信息
     */
    UserInfoVO loadCurrentUser();

    /**
     * 获取当前用户的菜单树
     *
     * @return 用户菜单树形结构
     */
    List<DtoMenu> getCurrentUserMenus();
}
