package com.hx.frame.core.service;

import com.hx.frame.commons.base.service.IBaseService;
import com.hx.frame.core.dto.DtoRole;
import com.hx.frame.core.dto.DtoUser;

import java.util.Collection;
import java.util.List;

/**
 * 用户服务接口
 * 定义用户相关的业务操作
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-18
 */
public interface UserService extends IBaseService<DtoUser> {

    /**
     * 重置用户密码
     *
     * @param userId 用户ID
     */
    void resetPassword(String userId);

    /**
     * 重写逻辑删除方法，同时删除用户角色关联
     *
     * @param ids 用户ID集合
     */
    @Override
    void logicDelete(Collection<String> ids);

    /**
     * 获取用户详情，包含角色信息
     *
     * @param id 用户ID
     * @return 用户详情
     */
    DtoUser getUserWithRoles(String id);

    /**
     * 为用户分配角色
     *
     * @param userId  用户ID
     * @param roleIds 角色ID列表
     */
    void assignRoles(String userId, List<String> roleIds);

    /**
     * 获取用户已分配的角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<DtoRole> getRolesByUserId(String userId);

    /**
     * 查询未分配指定角色的用户列表
     *
     * @param roleId 角色id
     * @return 未分配角色的用户列表
     */
    List<DtoUser> getUsersWithoutRoles(String roleId);
}
