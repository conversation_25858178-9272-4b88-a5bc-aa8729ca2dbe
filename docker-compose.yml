version: '3.8'
services:
  hx-frame-java:
    image: hx-frame-java
    container_name: hx-frame-java
    ports:
      - "8005:80"
    environment:
      - SERVER_PORT=80
      - ACTIVE_PROFILE=mysql
      - REDIS_HOST=*************
      - REDIS_PORT=8004
      - REDIS_PASSWORD=Hx**..123
      - REDIS_DATABASE=11
      - SERVER_CONTEXT_PATH=/
      - MYSQL_HOST=rm-uf6hlg78p8f36m81l7o.mysql.rds.aliyuncs.com
      - MYSQL_PORT=3306
      - MYSQL_DATABASE=hx_frame
      - MYSQL_USERNAME=wms01
      - MYSQL_PASSWORD=Sunpeng99
      - LOG_LEVEL=INFO
      - LOG_PATH=/hx-frame/data/logs/hx-frame.log
    volumes:
      - /baoyc/hx-frame/data/logs:/hx-frame/data/logs
    restart: always