FROM maven:3.8.4-openjdk-8
COPY hx-frame-bootstrap-1.0.0-SNAPSHOT.jar /app/app.jar
WORKDIR /app
ENV JAVA_OPTS=""
ENTRYPOINT ["java", "-server", "-Duser.timezone=GMT+08", "-Xms2048m","-Xmx2048m","-XX:CompressedClassSpaceSize=512m","-XX:+UseCompressedClassPointers","-XX:MetaspaceSize=128m","-XX:MaxMetaspaceSize=256m", "-XX:MaxNewSize=256m","-XX:MaxMetaspaceSize=512m", "-XX:+UseG1GC","-Djava.security.egd=file:/dev/./urandom", "-Dfile.encoding=utf-8","-jar","/app/app.jar"]