spring:
  profiles:
    active: ${ACTIVE_PROFILE:mysql}
  application:
    name: hx-frame
  jpa:
    database-platform: org.hibernate.dialect.MySQL8Dialect
    hibernate:
      ddl-auto: none
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  redis:
    host: ${REDIS_HOST:*************}
    port: ${REDIS_PORT:8004}
    password: ${REDIS_PASSWORD:Hx**..123}
    database: ${REDIS_DATABASE:11}
    timeout: 10000
    lettuce:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0

server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: ${SERVER_CONTEXT_PATH:/}
    encoding:
      charset: UTF-8
      force: true
      enabled: true

flyway:
  enabled: true
  baseline-on-migrate: true
  locations: classpath:db/migration
  table: flyway_schema_history
  validate-on-migrate: true
  driver-class-name: com.mysql.cj.jdbc.Driver
  url: jdbc:mysql://${MYSQL_HOST:rm-uf6hlg78p8f36m81l7o.mysql.rds.aliyuncs.com}:${MYSQL_PORT:3306}/${MYSQL_DATABASE:hx_frame}?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai&useSSL=false
  user: ${MYSQL_USERNAME:wms01}
  password: ${MYSQL_PASSWORD:Sunpeng99}
  baseline-version: 0
  baseline-description: baseline

logging:
  level:
    root: ${LOG_LEVEL:INFO}
    com.hx: ${LOG_LEVEL:DEBUG}
  file:
    name: ${LOG_PATH:logs/hx-frame.log}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

jwt:
  secret: 分享已知，学习未知!Make it happen and easy!
  access-token-validity: 60  # 访问令牌有效期（分钟）
  refresh-token-validity: 1440 # 刷新令牌有效期（分钟，24小时）
